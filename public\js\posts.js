// Post management system for Naroop
import { AppState, CoreUtils } from './core.js';
import { Authentication } from './authentication.js';

console.log('📝 Loading posts system...');

// Import error handler if available
let errorHandler = null;
try {
    const module = await import('./error-handler.js');
    errorHandler = module.errorHandler;
} catch (error) {
    console.warn('Error handler not available:', error);
}

export const Posts = {
    postsContainer: null,
    emptyState: null,
    loadingState: null,

    // Initialize posts system
    init() {
        console.log('🔧 Initializing posts system...');
        this.postsContainer = document.getElementById('postsContainer');
        this.emptyState = document.getElementById('emptyState');
        this.loadingState = document.getElementById('postsLoading');
        
        this.setupEventListeners();
        this.loadPosts();
        console.log('✅ Posts system initialized');
    },

    // Setup event listeners
    setupEventListeners() {
        // Create post button
        const createPostBtn = document.getElementById('createPostBtn');
        if (createPostBtn) {
            createPostBtn.addEventListener('click', () => this.openCreatePostModal());
        }

        // Post form submission
        const postForm = document.getElementById('postForm');
        if (postForm) {
            postForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitPost();
            });
        }
    },

    // Load posts from server
    async loadPosts() {
        try {
            this.showLoading();

            const response = await fetch('/api/posts', {
                credentials: 'include',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                const posts = await response.json();
                AppState.currentPosts = posts;
                this.renderAllPosts();

                if (errorHandler && posts.length === 0) {
                    errorHandler.showNotification('No posts yet. Be the first to share your story!', 'info', 4000);
                }
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw Object.assign(new Error(errorData.error || 'Failed to load posts'), {
                    status: response.status
                });
            }
        } catch (error) {
            console.error('Error loading posts:', error);

            if (errorHandler) {
                await errorHandler.handleApiError(error, {
                    method: 'GET',
                    url: '/api/posts'
                }, () => this.loadPosts());
            } else {
                this.showError('Failed to load posts. Please try again.');
            }
        } finally {
            this.hideLoading();
        }
    },

    // Render all posts
    renderAllPosts() {
        if (!this.postsContainer) return;

        this.postsContainer.innerHTML = '';
        
        if (AppState.currentPosts.length === 0) {
            this.showEmptyState();
        } else {
            this.hideEmptyState();
            AppState.currentPosts.forEach(post => {
                const postElement = this.createPostElement(post);
                this.postsContainer.appendChild(postElement);
            });
        }
    },

    // Create post element
    createPostElement(post) {
        const postDiv = document.createElement('div');
        postDiv.className = 'post-card';
        postDiv.dataset.postId = post.id;

        const timeAgo = CoreUtils.getTimeAgo(post.createdAt);
        const authorInitial = post.username ? post.username[0].toUpperCase() : 'U';

        postDiv.innerHTML = `
            <div class="post-header">
                <div class="post-author">
                    <div class="author-avatar">${authorInitial}</div>
                    <div class="author-info">
                        <div class="author-name">${CoreUtils.sanitizeHtml(post.username || 'Anonymous')}</div>
                        <div class="post-time">${timeAgo}</div>
                    </div>
                </div>
                <div class="post-menu">
                    <button class="post-menu-btn" onclick="Posts.showPostMenu('${post.id}')">⋯</button>
                </div>
            </div>
            <div class="post-content">
                <h3 class="post-title">${CoreUtils.sanitizeHtml(post.title)}</h3>
                <p class="post-text">${CoreUtils.sanitizeHtml(post.content)}</p>
                ${post.image ? `<img src="${post.image}" alt="Post image" class="post-image">` : ''}
            </div>
            <div class="post-actions">
                <button class="action-btn like-btn ${post.isLiked ? 'liked' : ''}" onclick="Posts.toggleLike('${post.id}')">
                    <span class="action-icon">❤️</span>
                    <span class="action-text">${post.likes || 0}</span>
                </button>
                <button class="action-btn comment-btn" onclick="Posts.showComments('${post.id}')">
                    <span class="action-icon">💬</span>
                    <span class="action-text">${post.comments || 0}</span>
                </button>
                <button class="action-btn share-btn" onclick="Posts.sharePost('${post.id}')">
                    <span class="action-icon">🔄</span>
                    <span class="action-text">${post.shares || 0}</span>
                </button>
            </div>
            <div class="post-stats">
                <span>${(post.likes || 0) + (post.comments || 0) + (post.shares || 0)} interactions</span>
            </div>
        `;

        return postDiv;
    },

    // Open create post modal
    openCreatePostModal() {
        const modal = document.getElementById('postModal');
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    },

    // Close create post modal
    closeCreatePostModal() {
        const modal = document.getElementById('postModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    },

    // Submit new post (now handled by form validator)
    async submitPost() {
        try {
            const title = document.getElementById('postTitle')?.value?.trim();
            const content = document.getElementById('postContent')?.value?.trim();

            // Basic validation (form validator handles detailed validation)
            if (!title || !content) {
                if (errorHandler) {
                    errorHandler.showNotification('Please fill in both title and content', 'error', 4000);
                } else {
                    this.showError('Please fill in both title and content');
                }
                return;
            }

            if (!Authentication.isAuthenticated()) {
                if (errorHandler) {
                    errorHandler.showNotification('You must be logged in to create a post', 'error', 4000);
                } else {
                    this.showError('You must be logged in to create a post');
                }
                return;
            }

            const currentUser = Authentication.getCurrentUser();
            const postData = {
                title,
                content,
                userId: currentUser.uid || currentUser.id,
                username: currentUser.username
            };

            // Show loading state
            const submitBtn = document.querySelector('#postForm button[type="submit"]');
            let hideLoading = null;

            if (errorHandler) {
                hideLoading = errorHandler.showLoading(submitBtn, 'Creating post...');
            } else {
                this.showPostLoading();
            }

            const response = await Authentication.makeAuthenticatedRequest('/api/posts', {
                method: 'POST',
                body: JSON.stringify(postData)
            });

            if (hideLoading) hideLoading();

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Add new post to the beginning of the array
                    AppState.currentPosts.unshift(result.post);

                    // Re-render posts
                    this.renderAllPosts();

                    // Close modal and reset form
                    this.closeCreatePostModal();
                    this.resetPostForm();

                    // Show success notification
                    if (errorHandler) {
                        errorHandler.showNotification('Post created successfully! 🎉', 'success', 4000);
                    }

                    console.log('✅ Post created successfully');
                } else {
                    throw new Error(result.error || 'Failed to create post');
                }
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw Object.assign(new Error(errorData.error || 'Failed to create post'), {
                    status: response.status
                });
            }
        } catch (error) {
            console.error('Error creating post:', error);

            if (errorHandler) {
                await errorHandler.handleApiError(error, {
                    method: 'POST',
                    url: '/api/posts'
                }, () => this.submitPost());
            } else {
                this.showError('Failed to create post. Please try again.');
            }
        } finally {
            if (!errorHandler) {
                this.hidePostLoading();
            }
        }
    },

    // Reset post form
    resetPostForm() {
        const titleInput = document.getElementById('postTitle');
        const contentInput = document.getElementById('postContent');
        
        if (titleInput) titleInput.value = '';
        if (contentInput) contentInput.value = '';
    },

    // Toggle like on post
    async toggleLike(postId) {
        try {
            if (!Authentication.isAuthenticated()) {
                this.showError('You must be logged in to like posts');
                return;
            }

            const currentUser = Authentication.getCurrentUser();
            const response = await Authentication.makeAuthenticatedRequest(`/api/posts/${postId}/like`, {
                method: 'POST',
                body: JSON.stringify({ userId: currentUser.uid || currentUser.id })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Update post in local state
                    const post = AppState.currentPosts.find(p => p.id === postId);
                    if (post) {
                        post.likes = result.likes;
                        post.isLiked = result.liked;
                        
                        // Update UI
                        this.updatePostLikeUI(postId, result.likes, result.liked);
                    }
                }
            }
        } catch (error) {
            console.error('Error toggling like:', error);
        }
    },

    // Update post like UI
    updatePostLikeUI(postId, likes, isLiked) {
        const postElement = document.querySelector(`[data-post-id="${postId}"]`);
        if (postElement) {
            const likeBtn = postElement.querySelector('.like-btn');
            const likeText = postElement.querySelector('.like-btn .action-text');
            
            if (likeBtn) {
                likeBtn.classList.toggle('liked', isLiked);
            }
            if (likeText) {
                likeText.textContent = likes;
            }
        }
    },

    // Show comments (placeholder)
    showComments(postId) {
        console.log(`Show comments for post: ${postId}`);
        // TODO: Implement comments modal
        alert('Comments feature coming soon!');
    },

    // Share post (placeholder)
    sharePost(postId) {
        console.log(`Share post: ${postId}`);
        // TODO: Implement share functionality
        alert('Share feature coming soon!');
    },

    // Show post menu (placeholder)
    showPostMenu(postId) {
        console.log(`Show menu for post: ${postId}`);
        // TODO: Implement post menu
        alert('Post menu coming soon!');
    },

    // Show loading state
    showLoading() {
        if (this.loadingState) {
            this.loadingState.style.display = 'block';
        }
    },

    // Hide loading state
    hideLoading() {
        if (this.loadingState) {
            this.loadingState.style.display = 'none';
        }
    },

    // Show post loading
    showPostLoading() {
        const postLoading = document.getElementById('postLoading');
        if (postLoading) {
            postLoading.style.display = 'block';
        }
    },

    // Hide post loading
    hidePostLoading() {
        const postLoading = document.getElementById('postLoading');
        if (postLoading) {
            postLoading.style.display = 'none';
        }
    },

    // Show empty state
    showEmptyState() {
        if (!this.postsContainer) return;

        this.postsContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">📝</div>
                <h3>No stories yet</h3>
                <p>Be the first to share your positive experience with the community. Your story could inspire others!</p>
            </div>
        `;
    },

    // Hide empty state
    hideEmptyState() {
        const emptyState = this.postsContainer?.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    },

    // Show error
    showError(message) {
        CoreUtils.showError(message);
    }
};

// Initialize posts when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for other modules to initialize
    setTimeout(() => {
        Posts.init();
    }, 200);
});

// Make posts available globally
window.Posts = Posts;
