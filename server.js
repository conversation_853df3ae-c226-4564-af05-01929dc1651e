const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');

// Load configuration and utilities
const config = require('./config');
const logger = require('./utils/logger');
const BackendPerformanceOptimizer = require('./middleware/performance');
const CDNManager = require('./config/cdn');

// Firebase Admin SDK (for server-side token verification)
let admin;
let firebaseAdminInitialized = false;

try {
    admin = require('firebase-admin');

    // Initialize Firebase Admin SDK
    if (!admin.apps.length) {
        // Check if we have admin configuration
        if (config.firebase.admin.project_id && config.firebase.admin.client_email && config.firebase.admin.private_key) {
            // Initialize with service account credentials
            admin.initializeApp({
                credential: admin.credential.cert(config.firebase.admin),
                projectId: config.firebase.admin.project_id
            });
            firebaseAdminInitialized = true;
            logger.info('Firebase Admin SDK initialized with service account credentials');
        } else {
            // Try to initialize with default credentials (for production environments)
            try {
                admin.initializeApp({
                    projectId: config.firebase.projectId
                });
                firebaseAdminInitialized = true;
                logger.info('Firebase Admin SDK initialized with default credentials');
            } catch (defaultError) {
                logger.warn('Firebase Admin SDK could not be initialized with default credentials:', defaultError.message);
                logger.warn('Server will fall back to client-side authentication only');
            }
        }
    } else {
        firebaseAdminInitialized = true;
        logger.info('Firebase Admin SDK already initialized');
    }
} catch (error) {
    logger.warn('Firebase Admin SDK not available. Install with: npm install firebase-admin');
    logger.warn('Server will fall back to client-side authentication only');
}

const app = express();
const PORT = config.server.port;

// Initialize performance optimizer
const performanceOptimizer = new BackendPerformanceOptimizer();
performanceOptimizer.initializeMiddleware(app);

// Initialize CDN manager
const cdnManager = new CDNManager();
app.use(cdnManager.assetMiddleware());

// Production middleware setup
if (config.isProduction()) {
    // Security middleware for production
    try {
        const helmet = require('helmet');
        if (config.security.enableHelmet) {
            app.use(helmet({
                contentSecurityPolicy: {
                    directives: {
                        defaultSrc: ["'self'"],
                        styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://fonts.googleapis.com"],
                        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-hashes'", "https://cdn.jsdelivr.net", "https://www.gstatic.com"],
                        scriptSrcAttr: ["'unsafe-inline'"],
                        imgSrc: ["'self'", "data:", "https:", "blob:"],
                        connectSrc: ["'self'", "https://www.gstatic.com"],
                        fontSrc: ["'self'", "https://cdn.jsdelivr.net", "https://fonts.gstatic.com"],
                        objectSrc: ["'none'"],
                        mediaSrc: ["'self'"],
                        frameSrc: ["'none'"]
                    }
                }
            }));
        }
    } catch (error) {
        logger.warn('Helmet not installed. Install with: npm install helmet');
    }

    // Compression middleware
    if (config.server.enableCompression) {
        try {
            const compression = require('compression');
            app.use(compression());
        } catch (error) {
            logger.warn('Compression not installed. Install with: npm install compression');
        }
    }

    // Rate limiting
    try {
        const rateLimit = require('express-rate-limit');
        const limiter = rateLimit({
            windowMs: config.rateLimit.windowMs,
            max: config.rateLimit.maxRequests,
            message: {
                error: 'Too many requests from this IP, please try again later.'
            },
            standardHeaders: true,
            legacyHeaders: false
        });
        app.use('/api/', limiter);
    } catch (error) {
        logger.warn('Express-rate-limit not installed. Install with: npm install express-rate-limit');
    }
}

// Security headers middleware (apply early)
app.use(addSecurityHeaders);

// Request logging middleware
app.use(logger.requestLogger());

// Enhanced CORS configuration with security considerations
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        // In production, be more restrictive
        if (config.isProduction()) {
            const allowedOrigins = [
                'https://naroop.com',
                'https://www.naroop.com',
                'https://naroop-451d1.web.app',
                'https://naroop-451d1.firebaseapp.com'
            ];

            if (allowedOrigins.indexOf(origin) !== -1) {
                callback(null, true);
            } else {
                logger.warn('CORS blocked origin:', origin);
                callback(new Error('Not allowed by CORS'));
            }
        } else {
            // In development, allow localhost and common development origins
            const devOrigins = [
                'http://localhost:3000',
                'http://localhost:3001',
                'http://127.0.0.1:3000',
                'http://127.0.0.1:3001'
            ];

            if (origin.startsWith('http://localhost') ||
                origin.startsWith('http://127.0.0.1') ||
                devOrigins.includes(origin)) {
                callback(null, true);
            } else {
                callback(null, true); // Allow all in development for now
            }
        }
    },
    credentials: true,
    optionsSuccessStatus: 200,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['X-Total-Count']
};

app.use(cors(corsOptions));

// Enhanced body parsing with security limits
app.use(bodyParser.json({
    limit: '1mb', // Reduced from 10mb for security
    strict: true,
    type: 'application/json'
}));
app.use(bodyParser.urlencoded({
    extended: true,
    limit: '1mb', // Reduced from 10mb for security
    parameterLimit: 100 // Limit number of parameters
}));

// Cookie parser for secure session management
try {
    const cookieParser = require('cookie-parser');
    app.use(cookieParser(config.security.sessionSecret));
    console.log('✅ Cookie parser initialized with secure secret');
} catch (error) {
    console.warn('⚠️ Cookie parser not available. Install with: npm install cookie-parser');
    console.warn('Secure session management will not work without cookie-parser');
}

// Trust proxy for production deployments
if (config.isProduction()) {
    app.set('trust proxy', 1);
}

// Serve static files (your existing HTML, CSS, JS)
app.use(express.static('.', {
    maxAge: config.isProduction() ? '1d' : 0,
    etag: true
}));

// Serve landing page for root route
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'landing.html'));
});

// Serve landing page explicitly
app.get('/landing.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'landing.html'));
});

// Serve main app page
app.get('/app', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Also serve index.html directly for compatibility
app.get('/index.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Data storage files
const USERS_FILE = config.storage.usersFile;
const POSTS_FILE = config.storage.postsFile;
const NOTIFICATIONS_FILE = config.storage.notificationsFile;
const MESSAGES_FILE = config.storage.messagesFile;

// Initialize data files if they don't exist
function initializeDataFiles() {
    if (!fs.existsSync(USERS_FILE)) {
        fs.writeFileSync(USERS_FILE, JSON.stringify([]));
    }
    if (!fs.existsSync(POSTS_FILE)) {
        fs.writeFileSync(POSTS_FILE, JSON.stringify([]));
    }
    if (!fs.existsSync(NOTIFICATIONS_FILE)) {
        fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify([]));
    }
    if (!fs.existsSync(MESSAGES_FILE)) {
        fs.writeFileSync(MESSAGES_FILE, JSON.stringify([]));
    }
}

// Helper functions to read/write data
function readUsers() {
    try {
        const data = fs.readFileSync(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

function writeUsers(users) {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
}

function readPosts() {
    try {
        const data = fs.readFileSync(POSTS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

function writePosts(posts) {
    fs.writeFileSync(POSTS_FILE, JSON.stringify(posts, null, 2));
}

function readNotifications() {
    try {
        const data = fs.readFileSync(NOTIFICATIONS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

function writeNotifications(notifications) {
    fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify(notifications, null, 2));
}

function readMessages() {
    try {
        const data = fs.readFileSync(MESSAGES_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

function writeMessages(messages) {
    fs.writeFileSync(MESSAGES_FILE, JSON.stringify(messages, null, 2));
}

// Simple content moderation
function moderateContent(text) {
    const bannedWords = ['spam', 'scam', 'hate', 'abuse']; // Basic list
    const lowerText = text.toLowerCase();

    for (const word of bannedWords) {
        if (lowerText.includes(word)) {
            return {
                isAllowed: false,
                reason: 'Content contains inappropriate language'
            };
        }
    }

    // Check for excessive caps (more than 70% uppercase)
    const capsCount = (text.match(/[A-Z]/g) || []).length;
    const letterCount = (text.match(/[A-Za-z]/g) || []).length;
    if (letterCount > 10 && capsCount / letterCount > 0.7) {
        return {
            isAllowed: false,
            reason: 'Excessive use of capital letters'
        };
    }

    return { isAllowed: true };
}

// Helper function to create notifications
function createNotification(userId, type, message, relatedUserId, relatedPostId = null) {
    const notifications = readNotifications();
    const newNotification = {
        id: Date.now().toString(),
        userId,
        type, // 'like', 'comment', 'follow', 'mention'
        message,
        relatedUserId,
        relatedPostId,
        read: false,
        createdAt: new Date().toISOString()
    };

    notifications.unshift(newNotification);

    // Keep only last 100 notifications per user to prevent file from growing too large
    const userNotifications = notifications.filter(n => n.userId === userId);
    if (userNotifications.length > 100) {
        const notificationsToKeep = notifications.filter(n => n.userId !== userId);
        const recentUserNotifications = userNotifications.slice(0, 100);
        writeNotifications([...notificationsToKeep, ...recentUserNotifications]);
    } else {
        writeNotifications(notifications);
    }
}

// Firebase Configuration Endpoint
app.get('/api/firebase-config', (req, res) => {
    // Check if Firebase configuration exists and has valid values
    if (!config.firebase ||
        !config.firebase.apiKey ||
        config.firebase.apiKey === 'your-firebase-api-key' ||
        config.firebase.apiKey.startsWith('your-')) {

        // Return a development/demo configuration
        console.log('Firebase not configured, returning demo config');
        return res.json({
            apiKey: "demo-api-key",
            authDomain: "demo-project.firebaseapp.com",
            projectId: "demo-project",
            storageBucket: "demo-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:demo",
            measurementId: "G-DEMO"
        });
    }

    // Only send client-side Firebase configuration
    const clientConfig = {
        apiKey: config.firebase.apiKey,
        authDomain: config.firebase.authDomain,
        projectId: config.firebase.projectId,
        storageBucket: config.firebase.storageBucket,
        messagingSenderId: config.firebase.messagingSenderId,
        appId: config.firebase.appId,
        measurementId: config.firebase.measurementId
    };

    // Check if all required config values are present
    const missingConfig = Object.entries(clientConfig)
        .filter(([key, value]) => !value || value.includes('your-'))
        .map(([key]) => key);

    if (missingConfig.length > 0) {
        return res.status(500).json({
            error: 'Firebase configuration incomplete',
            missing: missingConfig,
            message: 'Please update your Firebase configuration in the .env file'
        });
    }

    res.json(clientConfig);
});

// Enhanced Firebase Authentication Middleware with Security Improvements
async function verifyFirebaseToken(req, res, next) {
    try {
        const authHeader = req.headers.authorization;

        // Enhanced token validation
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            logger.warn('Authentication attempt without proper authorization header', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                path: req.path
            });
            return res.status(401).json({
                error: 'No authentication token provided',
                code: 'AUTH_TOKEN_MISSING'
            });
        }

        const token = authHeader.split('Bearer ')[1];

        // Validate token format
        if (!token || token.length < 10) {
            logger.warn('Invalid token format received', {
                ip: req.ip,
                tokenLength: token ? token.length : 0
            });
            return res.status(401).json({
                error: 'Invalid token format',
                code: 'AUTH_TOKEN_INVALID_FORMAT'
            });
        }

        if (!firebaseAdminInitialized || !admin) {
            logger.error('Firebase Admin not available, authentication failed');
            return res.status(500).json({
                error: 'Authentication service unavailable',
                code: 'AUTH_SERVICE_UNAVAILABLE'
            });
        }

        try {
            // Verify token with additional security checks
            const decodedToken = await admin.auth().verifyIdToken(token, true); // checkRevoked = true

            // Additional security validations
            const now = Math.floor(Date.now() / 1000);

            // Check token expiration with buffer
            if (decodedToken.exp <= now) {
                logger.warn('Expired token used', {
                    uid: decodedToken.uid,
                    exp: decodedToken.exp,
                    now: now
                });
                return res.status(401).json({
                    error: 'Token has expired',
                    code: 'AUTH_TOKEN_EXPIRED'
                });
            }

            // Check if token is too old (issued more than 24 hours ago)
            if (decodedToken.iat < (now - 86400)) {
                logger.warn('Very old token used', {
                    uid: decodedToken.uid,
                    iat: decodedToken.iat,
                    age: now - decodedToken.iat
                });
                return res.status(401).json({
                    error: 'Token is too old, please refresh',
                    code: 'AUTH_TOKEN_TOO_OLD'
                });
            }

            // Enhanced user object with security metadata
            req.user = {
                uid: decodedToken.uid,
                email: decodedToken.email,
                username: decodedToken.name || decodedToken.display_name || decodedToken.email.split('@')[0],
                emailVerified: decodedToken.email_verified,
                authTime: decodedToken.auth_time,
                issuedAt: decodedToken.iat,
                expiresAt: decodedToken.exp,
                // Security metadata
                tokenAge: now - decodedToken.iat,
                sessionValid: true,
                lastVerified: now
            };

            // Log successful authentication with security context
            logger.info('User authenticated successfully', {
                uid: req.user.uid,
                email: req.user.email,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                tokenAge: req.user.tokenAge,
                path: req.path
            });

            next();
        } catch (verificationError) {
            // Enhanced error logging for security monitoring
            logger.error('Token verification failed', {
                error: verificationError.message,
                code: verificationError.code,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                path: req.path,
                tokenPrefix: token.substring(0, 10) + '...'
            });

            // Map Firebase errors to standardized responses
            let errorResponse = {
                error: 'Invalid authentication token',
                code: 'AUTH_TOKEN_INVALID'
            };

            if (verificationError.code === 'auth/id-token-expired') {
                errorResponse = {
                    error: 'Authentication token has expired',
                    code: 'AUTH_TOKEN_EXPIRED'
                };
            } else if (verificationError.code === 'auth/id-token-revoked') {
                errorResponse = {
                    error: 'Authentication token has been revoked',
                    code: 'AUTH_TOKEN_REVOKED'
                };
            } else if (verificationError.code === 'auth/invalid-id-token') {
                errorResponse = {
                    error: 'Invalid authentication token format',
                    code: 'AUTH_TOKEN_MALFORMED'
                };
            }

            res.status(401).json({
                ...errorResponse,
                details: config.isDevelopment() ? verificationError.message : undefined
            });
        }
    } catch (error) {
        logger.error('Authentication middleware critical error', {
            error: error.message,
            stack: error.stack,
            ip: req.ip,
            path: req.path
        });
        res.status(500).json({
            error: 'Authentication service error',
            code: 'AUTH_SERVICE_ERROR'
        });
    }
}

// Enhanced optional authentication middleware with security logging
async function optionalFirebaseAuth(req, res, next) {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            // No token provided, continue without authentication
            req.user = null;
            req.authenticated = false;
            return next();
        }

        const token = authHeader.split('Bearer ')[1];

        if (!firebaseAdminInitialized || !admin) {
            // Firebase Admin not available, continue without authentication
            logger.warn('Optional auth - Firebase Admin not available');
            req.user = null;
            req.authenticated = false;
            return next();
        }

        try {
            const decodedToken = await admin.auth().verifyIdToken(token, true);

            // Same security checks as required auth
            const now = Math.floor(Date.now() / 1000);

            if (decodedToken.exp <= now || decodedToken.iat < (now - 86400)) {
                logger.warn('Optional auth - Invalid/expired token ignored', {
                    uid: decodedToken.uid,
                    expired: decodedToken.exp <= now,
                    tooOld: decodedToken.iat < (now - 86400)
                });
                req.user = null;
                req.authenticated = false;
                return next();
            }

            req.user = {
                uid: decodedToken.uid,
                email: decodedToken.email,
                username: decodedToken.name || decodedToken.display_name || decodedToken.email.split('@')[0],
                emailVerified: decodedToken.email_verified,
                authTime: decodedToken.auth_time,
                issuedAt: decodedToken.iat,
                expiresAt: decodedToken.exp,
                tokenAge: now - decodedToken.iat,
                sessionValid: true,
                lastVerified: now
            };
            req.authenticated = true;

            logger.info('Optional auth - User authenticated', {
                uid: req.user.uid,
                email: req.user.email,
                path: req.path
            });
        } catch (verificationError) {
            // Token verification failed, but continue without authentication
            logger.warn('Optional auth - Token verification failed', {
                error: verificationError.message,
                code: verificationError.code,
                path: req.path
            });
            req.user = null;
            req.authenticated = false;
        }

        next();
    } catch (error) {
        logger.error('Optional authentication middleware error', {
            error: error.message,
            path: req.path
        });
        // Continue without authentication on error
        req.user = null;
        req.authenticated = false;
        next();
    }
}

// Security middleware to add security headers
function addSecurityHeaders(req, res, next) {
    // Prevent clickjacking
    res.setHeader('X-Frame-Options', 'DENY');

    // Prevent MIME type sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Enable XSS protection
    res.setHeader('X-XSS-Protection', '1; mode=block');

    // Referrer policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

    // Remove server information
    res.removeHeader('X-Powered-By');

    next();
}

// Rate limiting for authentication endpoints
function createAuthRateLimit() {
    if (!config.isProduction()) {
        return (req, res, next) => next(); // Skip in development
    }

    try {
        const rateLimit = require('express-rate-limit');
        return rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 5, // Limit each IP to 5 requests per windowMs for auth endpoints
            message: {
                error: 'Too many authentication attempts, please try again later.',
                code: 'AUTH_RATE_LIMIT_EXCEEDED',
                retryAfter: 15 * 60 // 15 minutes in seconds
            },
            standardHeaders: true,
            legacyHeaders: false,
            // Skip successful requests
            skipSuccessfulRequests: true,
            // Custom key generator to include user agent
            keyGenerator: (req) => {
                return req.ip + ':' + (req.get('User-Agent') || 'unknown');
            }
        });
    } catch (error) {
        logger.warn('Rate limiting not available for auth endpoints');
        return (req, res, next) => next();
    }
}

// Secure session management endpoints

// Create secure session (replaces client-side token storage)
app.post('/api/auth/create-session', verifyFirebaseToken, async (req, res) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }

        // Create secure session data
        const sessionData = {
            uid: req.user.uid,
            email: req.user.email,
            username: req.user.username,
            emailVerified: req.user.emailVerified,
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            ipAddress: req.ip,
            userAgent: req.get('User-Agent')
        };

        // Set secure HTTP-only cookie
        const cookieOptions = {
            httpOnly: true, // Prevent XSS attacks
            secure: config.isProduction(), // HTTPS only in production
            sameSite: 'strict', // CSRF protection
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            path: '/'
        };

        // Store session ID in cookie (not the actual session data)
        const sessionId = require('crypto').randomBytes(32).toString('hex');
        res.cookie('sessionId', sessionId, cookieOptions);

        // In a real application, store session data in Redis or database
        // For now, we'll use a simple in-memory store (not production-ready)
        if (!global.sessions) {
            global.sessions = new Map();
        }
        global.sessions.set(sessionId, sessionData);

        logger.info('Secure session created', {
            uid: req.user.uid,
            email: req.user.email,
            sessionId: sessionId.substring(0, 8) + '...',
            ip: req.ip
        });

        res.json({
            success: true,
            message: 'Secure session created',
            user: {
                uid: sessionData.uid,
                email: sessionData.email,
                username: sessionData.username,
                emailVerified: sessionData.emailVerified
            }
        });
    } catch (error) {
        logger.error('Error creating secure session:', error);
        res.status(500).json({
            error: 'Failed to create session',
            code: 'SESSION_CREATE_ERROR'
        });
    }
});

// Clear secure session
app.post('/api/auth/clear-session', (req, res) => {
    try {
        const sessionId = req.cookies?.sessionId;

        if (sessionId && global.sessions) {
            global.sessions.delete(sessionId);
            logger.info('Secure session cleared', {
                sessionId: sessionId.substring(0, 8) + '...',
                ip: req.ip
            });
        }

        // Clear the cookie
        res.clearCookie('sessionId', {
            httpOnly: true,
            secure: config.isProduction(),
            sameSite: 'strict',
            path: '/'
        });

        res.json({ success: true, message: 'Session cleared' });
    } catch (error) {
        logger.error('Error clearing session:', error);
        res.status(500).json({
            error: 'Failed to clear session',
            code: 'SESSION_CLEAR_ERROR'
        });
    }
});

// Validate secure session
app.get('/api/auth/validate-session', (req, res) => {
    try {
        const sessionId = req.cookies?.sessionId;

        if (!sessionId || !global.sessions || !global.sessions.has(sessionId)) {
            return res.json({
                valid: false,
                authenticated: false,
                reason: 'No valid session found'
            });
        }

        const sessionData = global.sessions.get(sessionId);

        // Check session expiry (24 hours)
        const sessionAge = Date.now() - new Date(sessionData.createdAt).getTime();
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours

        if (sessionAge > maxAge) {
            global.sessions.delete(sessionId);
            res.clearCookie('sessionId');
            return res.json({
                valid: false,
                authenticated: false,
                reason: 'Session expired'
            });
        }

        // Update last activity
        sessionData.lastActivity = new Date().toISOString();
        global.sessions.set(sessionId, sessionData);

        res.json({
            valid: true,
            authenticated: true,
            user: {
                uid: sessionData.uid,
                email: sessionData.email,
                username: sessionData.username,
                emailVerified: sessionData.emailVerified
            },
            sessionInfo: {
                createdAt: sessionData.createdAt,
                lastActivity: sessionData.lastActivity,
                age: sessionAge
            }
        });
    } catch (error) {
        logger.error('Error validating session:', error);
        res.status(500).json({
            error: 'Failed to validate session',
            code: 'SESSION_VALIDATE_ERROR'
        });
    }
});

// API Routes

// Performance metrics endpoint
app.get('/api/metrics/performance', (req, res) => {
    try {
        const metrics = performanceOptimizer.getMetrics();
        res.json({
            success: true,
            metrics,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error getting performance metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get performance metrics'
        });
    }
});

// Performance metrics collection endpoint
app.post('/api/metrics/performance', (req, res) => {
    try {
        const clientMetrics = req.body;

        // Log client-side performance metrics
        logger.info('Client performance metrics:', {
            loadTime: clientMetrics.loadTime,
            renderTime: clientMetrics.renderTime,
            interactionTime: clientMetrics.interactionTime,
            memoryUsage: clientMetrics.memoryUsage,
            connection: clientMetrics.connection,
            userAgent: clientMetrics.userAgent
        });

        res.json({ success: true });
    } catch (error) {
        logger.error('Error processing client metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to process metrics'
        });
    }
});

// Cache management endpoint (admin only)
app.delete('/api/cache', (req, res) => {
    try {
        performanceOptimizer.clearCache();
        res.json({
            success: true,
            message: 'Cache cleared successfully'
        });
    } catch (error) {
        logger.error('Error clearing cache:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to clear cache'
        });
    }
});

// Like a post
app.post('/api/posts/:id/like', (req, res) => {
    const postId = req.params.id;
    const { userId } = req.body;

    if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
    }

    const posts = readPosts();
    const postIndex = posts.findIndex(p => p.id === postId);

    if (postIndex === -1) {
        return res.status(404).json({ error: 'Post not found' });
    }

    // Initialize likes array if it doesn't exist
    if (!posts[postIndex].likedBy) {
        posts[postIndex].likedBy = [];
    }

    // Check if user already liked the post
    const alreadyLiked = posts[postIndex].likedBy.includes(userId);

    if (alreadyLiked) {
        // Unlike the post
        posts[postIndex].likedBy = posts[postIndex].likedBy.filter(id => id !== userId);
        posts[postIndex].likes = Math.max(0, (posts[postIndex].likes || 0) - 1);
    } else {
        // Like the post
        posts[postIndex].likedBy.push(userId);
        posts[postIndex].likes = (posts[postIndex].likes || 0) + 1;

        // Create notification for post owner (if not liking own post)
        if (posts[postIndex].userId !== userId) {
            const users = readUsers();
            const liker = users.find(u => u.id === userId);
            if (liker) {
                createNotification(
                    posts[postIndex].userId,
                    'like',
                    `${liker.username} liked your story "${posts[postIndex].title}"`,
                    userId,
                    postId
                );
            }
        }
    }

    writePosts(posts);

    res.json({
        success: true,
        liked: !alreadyLiked,
        likes: posts[postIndex].likes
    });
});

// Add a comment to a post
app.post('/api/posts/:id/comment', (req, res) => {
    const postId = req.params.id;
    const { userId, username, content } = req.body;

    if (!userId || !username || !content) {
        return res.status(400).json({ error: 'All fields are required' });
    }

    const posts = readPosts();
    const postIndex = posts.findIndex(p => p.id === postId);

    if (postIndex === -1) {
        return res.status(404).json({ error: 'Post not found' });
    }

    // Initialize comments array if it doesn't exist
    if (!posts[postIndex].commentsList) {
        posts[postIndex].commentsList = [];
    }

    const newComment = {
        id: Date.now().toString(),
        userId,
        username,
        content,
        createdAt: new Date().toISOString()
    };

    posts[postIndex].commentsList.push(newComment);
    posts[postIndex].comments = posts[postIndex].commentsList.length;

    // Create notification for post owner (if not commenting on own post)
    if (posts[postIndex].userId !== userId) {
        createNotification(
            posts[postIndex].userId,
            'comment',
            `${username} commented on your story "${posts[postIndex].title}"`,
            userId,
            postId
        );
    }

    writePosts(posts);

    res.json({
        success: true,
        comment: newComment,
        comments: posts[postIndex].comments
    });
});

// Share a post
app.post('/api/posts/:id/share', (req, res) => {
    const postId = req.params.id;
    const { userId } = req.body;

    if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
    }

    const posts = readPosts();
    const postIndex = posts.findIndex(p => p.id === postId);

    if (postIndex === -1) {
        return res.status(404).json({ error: 'Post not found' });
    }

    // Initialize shares array if it doesn't exist
    if (!posts[postIndex].sharedBy) {
        posts[postIndex].sharedBy = [];
    }

    // Check if user already shared the post
    const alreadyShared = posts[postIndex].sharedBy.includes(userId);

    if (!alreadyShared) {
        posts[postIndex].sharedBy.push(userId);
        posts[postIndex].shares = (posts[postIndex].shares || 0) + 1;

        writePosts(posts);
    }

    res.json({
        success: true,
        shares: posts[postIndex].shares
    });
});

// Create authentication rate limiter
const authRateLimit = createAuthRateLimit();

// User profile creation (Firebase handles authentication, we just store profile data)
app.post('/api/register', authRateLimit, async (req, res) => {
    try {
        const { uid, username, email } = req.body;

        // Only accept Firebase-authenticated users
        if (!uid || !username || !email) {
            return res.status(400).json({ error: 'Firebase UID, username, and email are required' });
        }

        const users = readUsers();

        // Check if user already exists in our local database
        if (users.find(user => user.uid === uid || user.email === email || user.username === username)) {
            return res.status(400).json({ error: 'User already exists' });
        }

        // Create user profile in local database
        const newUser = {
            uid: uid,
            id: uid, // Use Firebase UID as ID
            username,
            email,
            createdAt: new Date().toISOString(),
            bio: "Excited to share my story and connect with the Naroop community",
            followers: 0,
            following: 0,
            stories: 0
        };

        users.push(newUser);
        writeUsers(users);

        res.json({ success: true, user: newUser });
    } catch (error) {
        console.error('User profile creation error:', error);
        res.status(500).json({ error: 'Profile creation failed' });
    }
});

// User profile retrieval (Firebase handles authentication, we just return profile data)
app.post('/api/login', authRateLimit, async (req, res) => {
    try {
        const { uid, email } = req.body;

        // Only accept Firebase-authenticated users
        if (!uid || !email) {
            return res.status(400).json({ error: 'Firebase UID and email are required' });
        }

        const users = readUsers();
        let user = users.find(u => u.uid === uid || u.email === email);

        // If user doesn't exist in our database, create a basic profile
        if (!user) {
            const newUser = {
                uid: uid,
                id: uid,
                username: email.split('@')[0],
                email: email,
                createdAt: new Date().toISOString(),
                bio: "Excited to share my story and connect with the Naroop community",
                followers: 0,
                following: 0,
                stories: 0
            };

            users.push(newUser);
            writeUsers(users);
            user = newUser;
        }

        res.json({ success: true, user: user });
    } catch (error) {
        console.error('User profile retrieval error:', error);
        res.status(500).json({ error: 'Profile retrieval failed' });
    }
});

// Get current user profile (requires authentication)
app.get('/api/user/profile', verifyFirebaseToken, (req, res) => {
    try {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }

        const users = readUsers();
        const user = users.find(u => u.uid === req.user.uid || u.email === req.user.email);

        if (!user) {
            return res.status(404).json({ error: 'User profile not found' });
        }

        // Return user profile (no password field exists anymore)
        res.json({ success: true, user: user });
    } catch (error) {
        logger.error('Get user profile error:', error);
        res.status(500).json({ error: 'Failed to get user profile' });
    }
});

// Check authentication status
app.get('/api/auth/status', optionalFirebaseAuth, (req, res) => {
    try {
        if (req.user) {
            const users = readUsers();
            const user = users.find(u => u.uid === req.user.uid || u.email === req.user.email);

            if (user) {
                const { password, ...userResponse } = user;
                res.json({
                    authenticated: true,
                    user: userResponse,
                    firebaseUser: {
                        uid: req.user.uid,
                        email: req.user.email,
                        emailVerified: req.user.emailVerified,
                        authTime: req.user.authTime,
                        issuedAt: req.user.issuedAt,
                        expiresAt: req.user.expiresAt
                    },
                    sessionInfo: {
                        tokenValid: true,
                        timeUntilExpiry: req.user.expiresAt ? (req.user.expiresAt * 1000) - Date.now() : null
                    }
                });
            } else {
                res.json({
                    authenticated: true,
                    user: null,
                    firebaseUser: {
                        uid: req.user.uid,
                        email: req.user.email,
                        emailVerified: req.user.emailVerified
                    },
                    message: 'Firebase user found but no local profile'
                });
            }
        } else {
            res.json({ authenticated: false, user: null });
        }
    } catch (error) {
        logger.error('Auth status check error:', error);
        res.status(500).json({ error: 'Failed to check authentication status' });
    }
});

// Validate session endpoint
app.post('/api/auth/validate-session', verifyFirebaseToken, (req, res) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                valid: false,
                error: 'No valid session found'
            });
        }

        const users = readUsers();
        const user = users.find(u => u.uid === req.user.uid || u.email === req.user.email);

        if (!user) {
            return res.status(404).json({
                valid: false,
                error: 'User profile not found'
            });
        }

        // Check token expiry
        const timeUntilExpiry = req.user.expiresAt ? (req.user.expiresAt * 1000) - Date.now() : null;
        const isExpiringSoon = timeUntilExpiry && timeUntilExpiry < (5 * 60 * 1000); // 5 minutes

        res.json({
            valid: true,
            user: user,
            sessionInfo: {
                tokenValid: true,
                timeUntilExpiry: timeUntilExpiry,
                isExpiringSoon: isExpiringSoon,
                shouldRefresh: isExpiringSoon
            }
        });
    } catch (error) {
        logger.error('Session validation error:', error);
        res.status(500).json({
            valid: false,
            error: 'Session validation failed'
        });
    }
});

// Refresh token endpoint
app.post('/api/auth/refresh-token', verifyFirebaseToken, (req, res) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                error: 'No valid session found'
            });
        }

        // If we reach here, the token was successfully verified
        // The client should get a new token from Firebase directly
        res.json({
            success: true,
            message: 'Token is valid',
            sessionInfo: {
                uid: req.user.uid,
                email: req.user.email,
                expiresAt: req.user.expiresAt,
                timeUntilExpiry: req.user.expiresAt ? (req.user.expiresAt * 1000) - Date.now() : null
            }
        });
    } catch (error) {
        logger.error('Token refresh validation error:', error);
        res.status(401).json({
            success: false,
            error: 'Token refresh failed'
        });
    }
});

// Content Validation and Moderation System
const ContentModerator = {
    // Profanity and inappropriate content detection
    profanityWords: [
        'damn', 'hell', 'crap', 'stupid', 'idiot', 'hate', 'kill', 'die', 'death',
        'fuck', 'shit', 'bitch', 'asshole', 'bastard', 'slut', 'whore'
    ],

    // Spam detection patterns
    spamPatterns: [
        /(.)\1{4,}/g, // Repeated characters
        /[A-Z]{5,}/g, // Excessive caps
        /(buy now|click here|free money|get rich|make money fast)/gi,
        /(www\.|http|\.com|\.net|\.org)/gi,
        /(\d{3}[-.]?\d{3}[-.]?\d{4})/g,
        /@\w+\.(com|net|org)/gi
    ],

    // Positive content keywords
    positiveKeywords: [
        'success', 'achievement', 'inspiration', 'community', 'help', 'support',
        'love', 'family', 'education', 'growth', 'progress', 'overcome',
        'grateful', 'blessed', 'proud', 'celebrate', 'unity', 'together'
    ],

    validateContent(title, content) {
        const results = {
            isValid: true,
            score: 100,
            warnings: [],
            errors: [],
            suggestions: [],
            moderationFlags: []
        };

        const fullText = `${title} ${content}`.toLowerCase();

        // Check for profanity
        const profanityCheck = this.checkProfanity(fullText);
        if (profanityCheck.found) {
            results.score -= 30;
            results.warnings.push(`Inappropriate language detected: ${profanityCheck.words.join(', ')}`);
            results.moderationFlags.push('profanity');

            if (profanityCheck.severity === 'high') {
                results.isValid = false;
                results.errors.push('Content contains severe inappropriate language.');
            }
        }

        // Check for spam
        const spamCheck = this.checkSpam(title, content);
        if (spamCheck.isSpam) {
            results.score -= 40;
            results.warnings.push(...spamCheck.reasons);
            results.moderationFlags.push('spam');

            if (spamCheck.severity === 'high') {
                results.isValid = false;
                results.errors.push('Content appears to be spam.');
            }
        }

        // Check content quality
        const qualityCheck = this.checkQuality(title, content);
        results.score += qualityCheck.bonus;
        results.warnings.push(...qualityCheck.warnings);
        results.suggestions.push(...qualityCheck.suggestions);

        // Check for positive content
        const positivityCheck = this.checkPositivity(fullText);
        results.score += positivityCheck.bonus;
        results.suggestions.push(...positivityCheck.suggestions);

        results.score = Math.max(0, Math.min(100, results.score));

        return results;
    },

    checkProfanity(text) {
        const found = [];
        let severity = 'low';

        this.profanityWords.forEach(word => {
            if (text.includes(word)) {
                found.push(word);
                if (['fuck', 'shit', 'bitch', 'hate', 'kill', 'die'].includes(word)) {
                    severity = 'high';
                }
            }
        });

        return { found: found.length > 0, words: found, severity };
    },

    checkSpam(title, content) {
        const reasons = [];
        let spamScore = 0;
        const fullText = `${title} ${content}`;

        this.spamPatterns.forEach(pattern => {
            const matches = fullText.match(pattern);
            if (matches) {
                spamScore += matches.length * 10;
                if (pattern.source.includes('buy now')) {
                    reasons.push('Contains promotional language');
                } else if (pattern.source.includes('www\\.|http')) {
                    reasons.push('Contains external links');
                } else if (pattern.source.includes('\\d{3}')) {
                    reasons.push('Contains phone numbers');
                } else if (pattern.source.includes('@\\w+')) {
                    reasons.push('Contains email addresses');
                }
            }
        });

        return {
            isSpam: spamScore > 20,
            severity: spamScore > 50 ? 'high' : 'medium',
            score: spamScore,
            reasons
        };
    },

    checkQuality(title, content) {
        const warnings = [];
        const suggestions = [];
        let bonus = 0;

        if (title.length < 10) {
            warnings.push('Title is quite short');
        } else if (title.length > 50) {
            bonus += 5;
        }

        if (content.length < 50) {
            warnings.push('Content is very short');
        } else if (content.length > 200) {
            bonus += 10;
        }

        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        if (sentences.length >= 3) {
            bonus += 5;
        }

        return { warnings, suggestions, bonus };
    },

    checkPositivity(text) {
        let bonus = 0;
        const suggestions = [];
        const foundPositive = [];

        this.positiveKeywords.forEach(keyword => {
            if (text.includes(keyword)) {
                foundPositive.push(keyword);
                bonus += 2;
            }
        });

        if (foundPositive.length === 0) {
            suggestions.push('Consider highlighting positive aspects of your story');
        }

        return { bonus: Math.min(bonus, 20), suggestions };
    }
};

// Content validation endpoint
app.post('/api/content/validate', (req, res) => {
    try {
        const { title, content } = req.body;

        if (!title || !content) {
            return res.status(400).json({
                error: 'Title and content are required'
            });
        }

        const validation = ContentModerator.validateContent(title, content);

        res.json({
            success: true,
            validation: validation
        });
    } catch (error) {
        logger.error('Content validation error:', error);
        res.status(500).json({
            error: 'Content validation failed'
        });
    }
});

// Report content endpoint
app.post('/api/content/report', verifyFirebaseToken, (req, res) => {
    try {
        const { postId, reason, details } = req.body;

        if (!postId || !reason) {
            return res.status(400).json({
                error: 'Post ID and reason are required'
            });
        }

        // Store the report (in a real app, this would go to a database)
        const report = {
            id: Date.now().toString(),
            postId: postId,
            reportedBy: req.user ? req.user.uid : 'anonymous',
            reason: reason,
            details: details || '',
            timestamp: new Date().toISOString(),
            status: 'pending'
        };

        logger.info('Content report received:', report);

        // TODO: Implement actual report storage and moderation workflow

        res.json({
            success: true,
            message: 'Report submitted successfully',
            reportId: report.id
        });
    } catch (error) {
        logger.error('Content report error:', error);
        res.status(500).json({
            error: 'Failed to submit report'
        });
    }
});

// Update user profile
app.put('/api/users/:id', (req, res) => {
    const userId = req.params.id;
    const { username, bio } = req.body;

    if (!username && !bio) {
        return res.status(400).json({ error: 'At least one field (username or bio) is required' });
    }

    const users = readUsers();
    const userIndex = users.findIndex(u => u.id === userId);

    if (userIndex === -1) {
        return res.status(404).json({ error: 'User not found' });
    }

    // Update user fields
    if (username) {
        // Check if username is already taken by another user
        const existingUser = users.find(u => u.username === username && u.id !== userId);
        if (existingUser) {
            return res.status(400).json({ error: 'Username already taken' });
        }
        users[userIndex].username = username;
    }

    if (bio !== undefined) {
        users[userIndex].bio = bio;
    }

    writeUsers(users);

    // Don't send password back
    const { password: _, ...userResponse } = users[userIndex];
    res.json({ success: true, user: userResponse });
});

// Follow/Unfollow a user
app.post('/api/users/:id/follow', (req, res) => {
    const targetUserId = req.params.id;
    const { userId } = req.body;

    if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
    }

    if (userId === targetUserId) {
        return res.status(400).json({ error: 'Cannot follow yourself' });
    }

    const users = readUsers();
    const currentUserIndex = users.findIndex(u => u.id === userId);
    const targetUserIndex = users.findIndex(u => u.id === targetUserId);

    if (currentUserIndex === -1 || targetUserIndex === -1) {
        return res.status(404).json({ error: 'User not found' });
    }

    // Initialize following arrays if they don't exist
    if (!users[currentUserIndex].followingList) {
        users[currentUserIndex].followingList = [];
    }
    if (!users[targetUserIndex].followersList) {
        users[targetUserIndex].followersList = [];
    }

    const isFollowing = users[currentUserIndex].followingList.includes(targetUserId);

    if (isFollowing) {
        // Unfollow
        users[currentUserIndex].followingList = users[currentUserIndex].followingList.filter(id => id !== targetUserId);
        users[targetUserIndex].followersList = users[targetUserIndex].followersList.filter(id => id !== userId);
        users[currentUserIndex].following = Math.max(0, (users[currentUserIndex].following || 0) - 1);
        users[targetUserIndex].followers = Math.max(0, (users[targetUserIndex].followers || 0) - 1);
    } else {
        // Follow
        users[currentUserIndex].followingList.push(targetUserId);
        users[targetUserIndex].followersList.push(userId);
        users[currentUserIndex].following = (users[currentUserIndex].following || 0) + 1;
        users[targetUserIndex].followers = (users[targetUserIndex].followers || 0) + 1;

        // Create notification for the followed user
        createNotification(
            targetUserId,
            'follow',
            `${users[currentUserIndex].username} joined your community and started following you`,
            userId
        );
    }

    writeUsers(users);

    res.json({
        success: true,
        following: !isFollowing,
        followers: users[targetUserIndex].followers,
        following_count: users[currentUserIndex].following
    });
});

// Get user's followers
app.get('/api/users/:id/followers', (req, res) => {
    const userId = req.params.id;
    const users = readUsers();
    const user = users.find(u => u.id === userId);

    if (!user) {
        return res.status(404).json({ error: 'User not found' });
    }

    const followers = users.filter(u => user.followersList && user.followersList.includes(u.id))
                           .map(u => ({ id: u.id, username: u.username, bio: u.bio }));

    res.json(followers);
});

// Get user's following
app.get('/api/users/:id/following', (req, res) => {
    const userId = req.params.id;
    const users = readUsers();
    const user = users.find(u => u.id === userId);

    if (!user) {
        return res.status(404).json({ error: 'User not found' });
    }

    const following = users.filter(u => user.followingList && user.followingList.includes(u.id))
                           .map(u => ({ id: u.id, username: u.username, bio: u.bio }));

    res.json(following);
});

// Get all users (for suggestions)
app.get('/api/users', (req, res) => {
    const users = readUsers();
    const publicUsers = users.map(u => ({
        id: u.id,
        username: u.username,
        bio: u.bio,
        followers: u.followers || 0,
        following: u.following || 0,
        stories: u.stories || 0,
        createdAt: u.createdAt
    }));

    res.json(publicUsers);
});

// Get user notifications
app.get('/api/notifications/:userId', (req, res) => {
    const userId = req.params.userId;
    const notifications = readNotifications();

    const userNotifications = notifications
        .filter(n => n.userId === userId)
        .slice(0, 20); // Return last 20 notifications

    res.json(userNotifications);
});

// Mark notification as read
app.put('/api/notifications/:id/read', (req, res) => {
    const notificationId = req.params.id;
    const notifications = readNotifications();

    const notificationIndex = notifications.findIndex(n => n.id === notificationId);
    if (notificationIndex === -1) {
        return res.status(404).json({ error: 'Notification not found' });
    }

    notifications[notificationIndex].read = true;
    writeNotifications(notifications);

    res.json({ success: true });
});

// Mark all notifications as read for a user
app.put('/api/notifications/:userId/read-all', (req, res) => {
    const userId = req.params.userId;
    const notifications = readNotifications();

    const updatedNotifications = notifications.map(n =>
        n.userId === userId ? { ...n, read: true } : n
    );

    writeNotifications(updatedNotifications);
    res.json({ success: true });
});

// Send a message
app.post('/api/messages', (req, res) => {
    const { senderId, receiverId, content } = req.body;

    if (!senderId || !receiverId || !content) {
        return res.status(400).json({ error: 'Sender ID, receiver ID, and content are required' });
    }

    if (senderId === receiverId) {
        return res.status(400).json({ error: 'Cannot send message to yourself' });
    }

    const users = readUsers();
    const sender = users.find(u => u.id === senderId);
    const receiver = users.find(u => u.id === receiverId);

    if (!sender || !receiver) {
        return res.status(404).json({ error: 'User not found' });
    }

    const messages = readMessages();
    const newMessage = {
        id: Date.now().toString(),
        senderId,
        receiverId,
        content: content.trim(),
        createdAt: new Date().toISOString(),
        read: false
    };

    messages.push(newMessage);
    writeMessages(messages);

    // Create notification for receiver
    createNotification(
        receiverId,
        'message',
        `${sender.username} from the Naroop community sent you a message`,
        senderId
    );

    res.json({ success: true, message: newMessage });
});

// Get conversations for a user
app.get('/api/conversations/:userId', (req, res) => {
    const userId = req.params.userId;
    const messages = readMessages();
    const users = readUsers();

    // Get all messages involving this user
    const userMessages = messages.filter(m =>
        m.senderId === userId || m.receiverId === userId
    );

    // Group by conversation partner
    const conversations = {};
    userMessages.forEach(message => {
        const partnerId = message.senderId === userId ? message.receiverId : message.senderId;
        if (!conversations[partnerId]) {
            conversations[partnerId] = [];
        }
        conversations[partnerId].push(message);
    });

    // Format conversations with partner info and last message
    const conversationList = Object.keys(conversations).map(partnerId => {
        const partner = users.find(u => u.id === partnerId);
        const conversationMessages = conversations[partnerId];
        const lastMessage = conversationMessages[conversationMessages.length - 1];
        const unreadCount = conversationMessages.filter(m =>
            m.receiverId === userId && !m.read
        ).length;

        return {
            partnerId,
            partnerName: partner ? partner.username : 'Unknown User',
            partnerBio: partner ? partner.bio : '',
            lastMessage: {
                content: lastMessage.content,
                createdAt: lastMessage.createdAt,
                isFromMe: lastMessage.senderId === userId
            },
            unreadCount,
            messageCount: conversationMessages.length
        };
    }).sort((a, b) => new Date(b.lastMessage.createdAt) - new Date(a.lastMessage.createdAt));

    res.json(conversationList);
});

// Get messages between two users
app.get('/api/messages/:userId1/:userId2', (req, res) => {
    const { userId1, userId2 } = req.params;
    const messages = readMessages();

    const conversationMessages = messages.filter(m =>
        (m.senderId === userId1 && m.receiverId === userId2) ||
        (m.senderId === userId2 && m.receiverId === userId1)
    ).sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

    res.json(conversationMessages);
});

// Mark messages as read
app.put('/api/messages/:userId1/:userId2/read', (req, res) => {
    const { userId1, userId2 } = req.params;
    const messages = readMessages();

    const updatedMessages = messages.map(m => {
        if (m.senderId === userId2 && m.receiverId === userId1 && !m.read) {
            return { ...m, read: true };
        }
        return m;
    });

    writeMessages(updatedMessages);
    res.json({ success: true });
});

// Get comments for a specific post
app.get('/api/posts/:id/comments', (req, res) => {
    const postId = req.params.id;
    const posts = readPosts();
    const post = posts.find(p => p.id === postId);

    if (!post) {
        return res.status(404).json({ error: 'Post not found' });
    }

    const comments = post.commentsList || [];
    res.json(comments);
});

// Search posts and users
app.get('/api/search', (req, res) => {
    const { q, type } = req.query;

    if (!q || q.trim().length < 2) {
        return res.status(400).json({ error: 'Search query must be at least 2 characters' });
    }

    const searchTerm = q.toLowerCase().trim();

    if (type === 'users' || !type) {
        const users = readUsers();
        const matchingUsers = users
            .filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                (user.bio && user.bio.toLowerCase().includes(searchTerm))
            )
            .map(u => ({
                id: u.id,
                username: u.username,
                bio: u.bio,
                followers: u.followers || 0,
                following: u.following || 0,
                stories: u.stories || 0
            }));

        if (type === 'users') {
            return res.json({ users: matchingUsers });
        }
    }

    if (type === 'posts' || !type) {
        const posts = readPosts();
        const matchingPosts = posts.filter(post =>
            post.title.toLowerCase().includes(searchTerm) ||
            post.content.toLowerCase().includes(searchTerm) ||
            post.username.toLowerCase().includes(searchTerm) ||
            (post.category && post.category.toLowerCase().includes(searchTerm))
        );

        if (type === 'posts') {
            return res.json({ posts: matchingPosts });
        }
    }

    // If no type specified, return both
    if (!type) {
        const users = readUsers();
        const posts = readPosts();

        const matchingUsers = users
            .filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                (user.bio && user.bio.toLowerCase().includes(searchTerm))
            )
            .map(u => ({
                id: u.id,
                username: u.username,
                bio: u.bio,
                followers: u.followers || 0,
                following: u.following || 0,
                stories: u.stories || 0
            }));

        const matchingPosts = posts.filter(post =>
            post.title.toLowerCase().includes(searchTerm) ||
            post.content.toLowerCase().includes(searchTerm) ||
            post.username.toLowerCase().includes(searchTerm) ||
            (post.category && post.category.toLowerCase().includes(searchTerm))
        );

        res.json({
            users: matchingUsers.slice(0, 5),
            posts: matchingPosts.slice(0, 10)
        });
    }
});

// Get all posts
app.get('/api/posts', (req, res) => {
    const posts = readPosts();
    res.json(posts);
});

// Get personalized feed for a user
app.get('/api/feed/:userId', (req, res) => {
    const userId = req.params.userId;
    const posts = readPosts();
    const users = readUsers();

    const user = users.find(u => u.id === userId);
    if (!user) {
        return res.status(404).json({ error: 'User not found' });
    }

    // Get user's following list
    const followingList = user.followingList || [];

    // Score posts based on various factors
    const scoredPosts = posts.map(post => {
        let score = 0;

        // Base score from engagement
        score += (post.likes || 0) * 3;
        score += (post.comments || 0) * 5;
        score += (post.shares || 0) * 4;

        // Boost posts from followed users
        if (followingList.includes(post.userId)) {
            score += 50;
        }

        // Boost recent posts
        const postAge = Date.now() - new Date(post.createdAt).getTime();
        const hoursOld = postAge / (1000 * 60 * 60);
        if (hoursOld < 24) {
            score += 20 - hoursOld; // More recent = higher score
        }

        // Boost posts with images
        if (post.image) {
            score += 10;
        }

        // Category preference (could be enhanced with user preferences)
        const popularCategories = ['Personal Experience', 'Inspiration', 'Technology'];
        if (popularCategories.includes(post.category)) {
            score += 5;
        }

        return { ...post, score };
    });

    // Sort by score (highest first) and return
    const personalizedFeed = scoredPosts
        .sort((a, b) => b.score - a.score)
        .map(({ score, ...post }) => post); // Remove score from response

    res.json(personalizedFeed);
});

// Create new post
app.post('/api/posts', (req, res) => {
    const { title, content, userId, username } = req.body;

    if (!title || !content || !userId || !username) {
        return res.status(400).json({ error: 'All fields are required' });
    }

    // Content moderation
    const titleModeration = moderateContent(title);
    const contentModeration = moderateContent(content);

    if (!titleModeration.isAllowed) {
        return res.status(400).json({ error: `Title rejected: ${titleModeration.reason}` });
    }

    if (!contentModeration.isAllowed) {
        return res.status(400).json({ error: `Content rejected: ${contentModeration.reason}` });
    }
    
    const posts = readPosts();
    
    const newPost = {
        id: Date.now().toString(),
        title,
        content,
        userId,
        username,
        createdAt: new Date().toISOString(),
        likes: 0,
        comments: 0,
        shares: 0,
        category: "Personal Experience"
    };
    
    posts.unshift(newPost); // Add to beginning of array
    writePosts(posts);
    
    // Update user's story count
    const users = readUsers();
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
        users[userIndex].stories += 1;
        writeUsers(users);
    }
    
    res.json({ success: true, post: newPost });
});

// Health check endpoint
app.get('/health', (req, res) => {
    const healthCheck = {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.NODE_ENV,
        version: require('./package.json').version
    };

    res.status(200).json(healthCheck);
});

// Error handling middleware
app.use(logger.errorLogger());

// Global error handler
app.use((error, req, res, next) => {
    logger.error('Global error handler', {
        error: error.message,
        stack: config.NODE_ENV === 'development' ? error.stack : undefined,
        method: req.method,
        url: req.url
    });

    res.status(error.status || 500).json({
        error: config.NODE_ENV === 'production' ? 'Internal server error' : error.message,
        ...(config.NODE_ENV === 'development' && { stack: error.stack })
    });
});

// 404 handler
app.use((req, res) => {
    logger.warn('404 Not Found', {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip
    });

    res.status(404).json({
        error: 'Not Found',
        message: 'The requested resource was not found'
    });
});

// Initialize data files
initializeDataFiles();

// Graceful shutdown handling
process.on('SIGTERM', () => {
    logger.shutdown('SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    logger.shutdown('SIGINT received, shutting down gracefully');
    process.exit(0);
});

// Start server with enhanced error handling
const server = app.listen(PORT, config.server.host, () => {
    logger.startup(`Naroop server running on http://${config.server.host}:${PORT}`, {
        environment: config.NODE_ENV,
        port: PORT,
        host: config.server.host,
        compression: config.server.enableCompression,
        helmet: config.security.enableHelmet,
        cors: config.security.enableCors
    });

    if (config.NODE_ENV === 'development') {
        console.log('📱 Your social media platform is ready!');
        console.log(`🌐 Open http://localhost:${PORT} in your browser`);
        console.log('🔧 Development mode - detailed logging enabled');
    }
});

// Handle server startup errors
server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${PORT} is already in use`);
        process.exit(1);
    } else {
        logger.error('Server startup error', { error: error.message });
        process.exit(1);
    }
});
